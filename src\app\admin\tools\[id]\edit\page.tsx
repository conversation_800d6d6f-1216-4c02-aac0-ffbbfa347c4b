'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { apiClient } from '@/lib/api';
import { DbTool } from '@/lib/types';

interface EditToolPageProps {
  params: {
    id: string;
  };
}

export default function EditToolPage({ params }: EditToolPageProps) {
  const router = useRouter();
  const [tool, setTool] = useState<DbTool | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadTool = async () => {
      try {
        setLoading(true);
        // For now, we'll redirect back to admin since we don't have a full edit form yet
        // In the future, this would load the tool data and show an edit form
        const adminApiKey = 'admin-dashboard-access';
        
        // Try to get the tool to verify it exists
        const toolsResult = await apiClient.getAdminTools({ limit: 1000 }, adminApiKey);
        const foundTool = toolsResult.data.find(t => t.id === params.id);
        
        if (!foundTool) {
          setError('Tool not found');
          return;
        }

        // For now, just show a placeholder and redirect back
        setTimeout(() => {
          router.push('/admin');
        }, 2000);
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load tool');
      } finally {
        setLoading(false);
      }
    };

    loadTool();
  }, [params.id, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Loading tool...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-white mb-2">Error</h2>
          <p className="text-gray-300 mb-6">{error}</p>
          <button
            onClick={() => router.push('/admin')}
            className="bg-orange-500 hover:bg-orange-600 px-6 py-2 rounded-lg text-white font-medium"
          >
            Back to Admin
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
      <div className="text-center">
        <div className="text-green-400 text-6xl mb-4">✓</div>
        <h2 className="text-xl font-semibold text-white mb-2">Tool Created Successfully!</h2>
        <p className="text-gray-300 mb-6">
          The tool has been created and saved as a draft. Redirecting back to admin panel...
        </p>
        <div className="text-sm text-gray-400">
          Edit functionality will be available in the next update.
        </div>
      </div>
    </div>
  );
}
