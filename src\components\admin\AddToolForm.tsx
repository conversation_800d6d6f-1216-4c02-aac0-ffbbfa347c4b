'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { apiClient } from '@/lib/api';
import { Button } from '@/components/ui/Button';
import { AICategory } from '@/lib/types';

// Validation schema for Add Tool Form
const addToolSchema = z.object({
  // Basic Information
  name: z.string().min(1, 'Tool name is required').max(255, 'Tool name must be less than 255 characters'),
  slug: z.string().optional(), // Auto-generated from name
  description: z.string().min(10, 'Description must be at least 10 characters').max(500, 'Description must be less than 500 characters'),
  short_description: z.string().max(150, 'Short description must be less than 150 characters').optional(),
  detailed_description: z.string().min(50, 'Detailed description must be at least 50 characters').max(2000, 'Detailed description must be less than 2000 characters').optional(),
  
  // URLs
  link: z.string().url('Please enter a valid URL for the tool'),
  website: z.string().url('Please enter a valid website URL').optional().or(z.literal('')),
  logo_url: z.string().url('Please enter a valid logo URL').optional().or(z.literal('')),
  
  // Categorization
  category_id: z.string().min(1, 'Please select a category'),
  subcategory: z.string().optional(),
  
  // Company Information
  company: z.string().max(255, 'Company name must be less than 255 characters').optional(),
  founded: z.string().max(100, 'Founded year must be less than 100 characters').optional(),
  
  // Features (JSON array)
  features: z.string().optional(), // Will be parsed as JSON array
  
  // Metadata
  meta_title: z.string().max(255, 'Meta title must be less than 255 characters').optional(),
  meta_description: z.string().max(500, 'Meta description must be less than 500 characters').optional(),
  
  // Status and Workflow
  content_status: z.enum(['draft', 'published', 'archived']).optional().default('draft'),
  submission_type: z.enum(['admin', 'user_url', 'user_full']).optional().default('admin'),
  submission_source: z.string().optional().default('admin_panel'),
  
  // Verification
  is_verified: z.boolean().default(false),
  is_claimed: z.boolean().default(false),
});

type AddToolFormData = z.infer<typeof addToolSchema>;

interface AddToolFormProps {
  onSuccess?: (toolId: string) => void;
  onCancel?: () => void;
}

export function AddToolForm({ onSuccess, onCancel }: AddToolFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [categories, setCategories] = useState<AICategory[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<AddToolFormData>({
    resolver: zodResolver(addToolSchema),
    mode: 'onChange',
    defaultValues: {
      content_status: 'draft',
      submission_type: 'admin',
      submission_source: 'admin_panel',
      is_verified: false,
      is_claimed: false,
    }
  });

  // Watch name field to auto-generate slug
  const watchedName = watch('name');

  // Auto-generate slug from name
  useEffect(() => {
    if (watchedName) {
      const slug = watchedName
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-|-$/g, '');
      setValue('slug', slug);
    }
  }, [watchedName, setValue]);

  // Load categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoadingCategories(true);
        const categoriesData = await apiClient.getCategories();
        setCategories(categoriesData);
      } catch (error) {
        console.error('Failed to load categories:', error);
        setSubmitError('Failed to load categories. Please refresh the page.');
      } finally {
        setLoadingCategories(false);
      }
    };

    loadCategories();
  }, []);

  const onSubmit = async (data: AddToolFormData) => {
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Parse features if provided
      let parsedFeatures = null;
      if (data.features && data.features.trim()) {
        try {
          // Try to parse as JSON array, or split by newlines/commas
          if (data.features.trim().startsWith('[')) {
            parsedFeatures = JSON.parse(data.features);
          } else {
            // Split by newlines or commas and clean up
            parsedFeatures = data.features
              .split(/[\n,]/)
              .map(feature => feature.trim())
              .filter(feature => feature.length > 0);
          }
        } catch (parseError) {
          // If JSON parsing fails, treat as comma/newline separated list
          parsedFeatures = data.features
            .split(/[\n,]/)
            .map(feature => feature.trim())
            .filter(feature => feature.length > 0);
        }
      }

      // Prepare tool data for API
      const toolData = {
        ...data,
        features: parsedFeatures,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Get admin API key from environment or session
      const adminApiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'admin-dashboard-access';

      // Create tool via admin API
      const newTool = await apiClient.createAdminTool(toolData, adminApiKey);
      
      setSuccess(true);
      reset();

      if (onSuccess && newTool.id) {
        setTimeout(() => onSuccess(newTool.id), 2000);
      }
    } catch (err) {
      setSubmitError(err instanceof Error ? err.message : 'Failed to create tool');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-8 text-center">
        <div className="text-green-400 text-6xl mb-4">✓</div>
        <h3 className="text-xl font-semibold text-white mb-2">Tool Created Successfully!</h3>
        <p className="text-gray-300 mb-6">
          The new AI tool has been created and saved as a draft. You can now edit it further or publish it.
        </p>
        {onCancel && (
          <Button onClick={onCancel} variant="primary">
            Back to Tools
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-white">Add New AI Tool</h2>
        {onCancel && (
          <Button onClick={onCancel} variant="outline" size="sm">
            Cancel
          </Button>
        )}
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {submitError && (
          <div className="bg-red-900/20 border border-red-500 rounded-lg p-4">
            <p className="text-red-400 text-sm">{submitError}</p>
          </div>
        )}

        {/* Basic Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white border-b border-zinc-600 pb-2">
            Basic Information
          </h3>
          
          {/* Tool Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
              Tool Name *
            </label>
            <input
              type="text"
              id="name"
              {...register('name')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                errors.name ? 'border-red-500' : 'border-zinc-600'
              }`}
              placeholder="Enter the name of the AI tool"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-400">{errors.name.message}</p>
            )}
          </div>

          {/* Tool URL */}
          <div>
            <label htmlFor="link" className="block text-sm font-medium text-gray-300 mb-2">
              Tool URL *
            </label>
            <input
              type="url"
              id="link"
              {...register('link')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                errors.link ? 'border-red-500' : 'border-zinc-600'
              }`}
              placeholder="https://example-ai-tool.com"
            />
            {errors.link && (
              <p className="mt-1 text-sm text-red-400">{errors.link.message}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-2">
              Description *
            </label>
            <textarea
              id="description"
              rows={3}
              {...register('description')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical ${
                errors.description ? 'border-red-500' : 'border-zinc-600'
              }`}
              placeholder="Brief description of what this AI tool does..."
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-400">{errors.description.message}</p>
            )}
          </div>
        </div>

        {/* Categorization Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white border-b border-zinc-600 pb-2">
            Categorization
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Category */}
            <div>
              <label htmlFor="category_id" className="block text-sm font-medium text-gray-300 mb-2">
                Category *
              </label>
              {loadingCategories ? (
                <div className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-gray-400">
                  Loading categories...
                </div>
              ) : (
                <select
                  id="category_id"
                  {...register('category_id')}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.category_id ? 'border-red-500' : 'border-zinc-600'
                  }`}
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.title}
                    </option>
                  ))}
                </select>
              )}
              {errors.category_id && (
                <p className="mt-1 text-sm text-red-400">{errors.category_id.message}</p>
              )}
            </div>

            {/* Subcategory */}
            <div>
              <label htmlFor="subcategory" className="block text-sm font-medium text-gray-300 mb-2">
                Subcategory
              </label>
              <input
                type="text"
                id="subcategory"
                {...register('subcategory')}
                className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="Optional subcategory"
              />
            </div>
          </div>
        </div>

        {/* Additional Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white border-b border-zinc-600 pb-2">
            Additional Information
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Website URL */}
            <div>
              <label htmlFor="website" className="block text-sm font-medium text-gray-300 mb-2">
                Website URL
              </label>
              <input
                type="url"
                id="website"
                {...register('website')}
                className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                  errors.website ? 'border-red-500' : 'border-zinc-600'
                }`}
                placeholder="https://company-website.com"
              />
              {errors.website && (
                <p className="mt-1 text-sm text-red-400">{errors.website.message}</p>
              )}
            </div>

            {/* Logo URL */}
            <div>
              <label htmlFor="logo_url" className="block text-sm font-medium text-gray-300 mb-2">
                Logo URL
              </label>
              <input
                type="url"
                id="logo_url"
                {...register('logo_url')}
                className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                  errors.logo_url ? 'border-red-500' : 'border-zinc-600'
                }`}
                placeholder="https://example.com/logo.png"
              />
              {errors.logo_url && (
                <p className="mt-1 text-sm text-red-400">{errors.logo_url.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Company */}
            <div>
              <label htmlFor="company" className="block text-sm font-medium text-gray-300 mb-2">
                Company
              </label>
              <input
                type="text"
                id="company"
                {...register('company')}
                className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="Company or organization name"
              />
            </div>

            {/* Founded */}
            <div>
              <label htmlFor="founded" className="block text-sm font-medium text-gray-300 mb-2">
                Founded
              </label>
              <input
                type="text"
                id="founded"
                {...register('founded')}
                className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="e.g., 2023 or March 2023"
              />
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white border-b border-zinc-600 pb-2">
            Content
          </h3>

          {/* Short Description */}
          <div>
            <label htmlFor="short_description" className="block text-sm font-medium text-gray-300 mb-2">
              Short Description
            </label>
            <input
              type="text"
              id="short_description"
              {...register('short_description')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                errors.short_description ? 'border-red-500' : 'border-zinc-600'
              }`}
              placeholder="Brief one-line description (max 150 characters)"
              maxLength={150}
            />
            {errors.short_description && (
              <p className="mt-1 text-sm text-red-400">{errors.short_description.message}</p>
            )}
          </div>

          {/* Detailed Description */}
          <div>
            <label htmlFor="detailed_description" className="block text-sm font-medium text-gray-300 mb-2">
              Detailed Description
            </label>
            <textarea
              id="detailed_description"
              rows={4}
              {...register('detailed_description')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical ${
                errors.detailed_description ? 'border-red-500' : 'border-zinc-600'
              }`}
              placeholder="Comprehensive description of the tool's features and capabilities..."
            />
            {errors.detailed_description && (
              <p className="mt-1 text-sm text-red-400">{errors.detailed_description.message}</p>
            )}
          </div>

          {/* Features */}
          <div>
            <label htmlFor="features" className="block text-sm font-medium text-gray-300 mb-2">
              Features
            </label>
            <textarea
              id="features"
              rows={4}
              {...register('features')}
              className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical"
              placeholder="Enter features one per line or as JSON array:&#10;• AI-powered content generation&#10;• Real-time collaboration&#10;• Multi-language support"
            />
            <p className="mt-1 text-xs text-gray-400">
              Enter features one per line or as a JSON array (e.g., ["Feature 1", "Feature 2"])
            </p>
          </div>
        </div>

        {/* SEO Metadata Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white border-b border-zinc-600 pb-2">
            SEO Metadata
          </h3>

          {/* Meta Title */}
          <div>
            <label htmlFor="meta_title" className="block text-sm font-medium text-gray-300 mb-2">
              Meta Title
            </label>
            <input
              type="text"
              id="meta_title"
              {...register('meta_title')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                errors.meta_title ? 'border-red-500' : 'border-zinc-600'
              }`}
              placeholder="SEO title for search engines (max 255 characters)"
              maxLength={255}
            />
            {errors.meta_title && (
              <p className="mt-1 text-sm text-red-400">{errors.meta_title.message}</p>
            )}
          </div>

          {/* Meta Description */}
          <div>
            <label htmlFor="meta_description" className="block text-sm font-medium text-gray-300 mb-2">
              Meta Description
            </label>
            <textarea
              id="meta_description"
              rows={3}
              {...register('meta_description')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical ${
                errors.meta_description ? 'border-red-500' : 'border-zinc-600'
              }`}
              placeholder="SEO description for search engines (max 500 characters)"
              maxLength={500}
            />
            {errors.meta_description && (
              <p className="mt-1 text-sm text-red-400">{errors.meta_description.message}</p>
            )}
          </div>
        </div>

        {/* Status and Settings Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white border-b border-zinc-600 pb-2">
            Status & Settings
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Content Status */}
            <div>
              <label htmlFor="content_status" className="block text-sm font-medium text-gray-300 mb-2">
                Content Status
              </label>
              <select
                id="content_status"
                {...register('content_status')}
                className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
                <option value="archived">Archived</option>
              </select>
            </div>

            {/* Submission Type */}
            <div>
              <label htmlFor="submission_type" className="block text-sm font-medium text-gray-300 mb-2">
                Submission Type
              </label>
              <select
                id="submission_type"
                {...register('submission_type')}
                className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              >
                <option value="admin">Admin</option>
                <option value="user_url">User URL</option>
                <option value="user_full">User Full</option>
              </select>
            </div>
          </div>

          {/* Verification Checkboxes */}
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_verified"
                {...register('is_verified')}
                className="w-4 h-4 text-orange-500 bg-zinc-700 border-zinc-600 rounded focus:ring-orange-500 focus:ring-2"
              />
              <label htmlFor="is_verified" className="ml-2 text-sm text-gray-300">
                Verified Tool (shows blue checkmark)
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_claimed"
                {...register('is_claimed')}
                className="w-4 h-4 text-orange-500 bg-zinc-700 border-zinc-600 rounded focus:ring-orange-500 focus:ring-2"
              />
              <label htmlFor="is_claimed" className="ml-2 text-sm text-gray-300">
                Claimed by Company
              </label>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-between pt-6 border-t border-zinc-600">
          <div className="text-sm text-gray-400">
            * Required fields
          </div>

          <div className="flex items-center space-x-3">
            {onCancel && (
              <button
                type="button"
                onClick={onCancel}
                disabled={isSubmitting}
                className="px-4 py-2 bg-zinc-700 hover:bg-zinc-600 text-white rounded-md border border-zinc-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
            )}

            <button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px] px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Creating...</span>
                </div>
              ) : (
                'Create Tool'
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
