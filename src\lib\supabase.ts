import { createClient } from '@supabase/supabase-js';
import { AITool, DbTool, AICategory, ToolFilters, AdminToolFilters, PaginationInfo } from './types';

// Validate environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable');
}

if (!supabaseAnonKey) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable');
}

// Client for browser usage
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Admin client for server-side operations (only available server-side)
export const supabaseAdmin = supabaseServiceK<PERSON>
  ? createClient(supabaseUrl, supabaseServiceKey)
  : null;

// Transform database tool to frontend AITool type
function transformDbToolToAITool(dbTool: DbTool): AITool {
  // Helper function to safely parse JSON fields
  const safeJsonParse = (jsonString: any) => {
    if (!jsonString) return undefined;
    if (typeof jsonString === 'object') return jsonString; // Already parsed
    try {
      return JSON.parse(jsonString);
    } catch {
      return undefined;
    }
  };

  return {
    // Core fields - map database snake_case to frontend camelCase
    id: dbTool.id,
    name: dbTool.name,
    slug: dbTool.slug,
    logoUrl: dbTool.logo_url || '',
    description: dbTool.description || '',
    shortDescription: dbTool.short_description,
    detailedDescription: dbTool.detailed_description,
    link: dbTool.link,
    website: dbTool.website,
    category: dbTool.category_id || '',
    subcategory: dbTool.subcategory,
    company: dbTool.company,
    founded: dbTool.founded,

    // Verification and claiming
    isVerified: dbTool.is_verified,
    isClaimed: dbTool.is_claimed,
    claimInfo: safeJsonParse(dbTool.claim_info),

    // Content fields
    features: safeJsonParse(dbTool.features),
    screenshots: safeJsonParse(dbTool.screenshots),
    pricing: safeJsonParse(dbTool.pricing),
    socialLinks: safeJsonParse(dbTool.social_links),
    prosAndCons: safeJsonParse(dbTool.pros_and_cons),
    releases: safeJsonParse(dbTool.releases),
    haiku: safeJsonParse(dbTool.haiku),
    hashtags: safeJsonParse(dbTool.hashtags),
    tags: Array.isArray(dbTool.hashtags) ? dbTool.hashtags.map((tag: string) => ({ type: tag as any })) : undefined,

    // SEO fields
    metaTitle: dbTool.meta_title,
    metaDescription: dbTool.meta_description,

    // Status and timestamps
    contentStatus: dbTool.content_status,
    generatedContent: safeJsonParse(dbTool.generated_content),
    createdAt: dbTool.created_at,
    updatedAt: dbTool.updated_at,
    publishedAt: dbTool.published_at,

    // Enhanced AI System fields
    scrapedData: safeJsonParse(dbTool.scraped_data),
    aiGenerationStatus: dbTool.ai_generation_status,
    lastScrapedAt: dbTool.last_scraped_at,
    editorialReviewId: dbTool.editorial_review_id,
    aiGenerationJobId: dbTool.ai_generation_job_id,
    submissionType: dbTool.submission_type,
    submissionSource: dbTool.submission_source,
    contentQualityScore: dbTool.content_quality_score,
    lastAiUpdate: dbTool.last_ai_update,
  };
}

// Transform database category to frontend AICategory type
function transformDbCategoryToAICategory(dbCategory: any): AICategory {
  return {
    id: dbCategory.id || '',
    title: dbCategory.title || '',
    iconName: dbCategory.icon_name || 'Grid',
    description: dbCategory.description || '',
    tools: [], // Will be populated separately
    totalToolsCount: 0, // Will be calculated
    seeAllButton: {
      colorClass: dbCategory.color_class || 'bg-blue-500 hover:bg-blue-400',
      textColorClass: dbCategory.text_color_class || 'text-white',
    },
  };
}

// Database helper functions
export async function getTools(filters: ToolFilters = {}): Promise<{
  data: AITool[];
  pagination: PaginationInfo;
}> {
  let query = supabase
    .from('tools')
    .select('*', { count: 'exact' })
    .eq('content_status', 'published');

  // Apply filters
  if (filters.category) {
    query = query.eq('category_id', filters.category);
  }

  if (filters.subcategory) {
    query = query.eq('subcategory', filters.subcategory);
  }

  if (filters.search) {
    query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
  }

  if (filters.pricing) {
    query = query.eq('pricing->type', filters.pricing);
  }

  if (filters.verified !== undefined) {
    query = query.eq('is_verified', filters.verified);
  }

  // Apply sorting
  const sortBy = filters.sortBy || 'created_at';
  const sortOrder = filters.sortOrder || 'desc';
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });

  // Apply pagination
  const page = filters.page || 1;
  const limit = filters.limit || 20;
  const from = (page - 1) * limit;
  const to = from + limit - 1;

  query = query.range(from, to);

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to fetch tools: ${error.message}`);
  }

  return {
    data: data?.map(transformDbToolToAITool) || [],
    pagination: {
      currentPage: page,
      totalPages: Math.ceil((count || 0) / limit),
      totalItems: count || 0,
      itemsPerPage: limit,
    },
  };
}

// Admin-specific function to get all tools regardless of status
export async function getAdminTools(filters: AdminToolFilters = {}): Promise<{
  data: AITool[];
  pagination: PaginationInfo;
}> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  let query = supabaseAdmin
    .from('tools')
    .select('*', { count: 'exact' });

  // Apply admin-specific filters
  if (filters.category) {
    query = query.eq('category_id', filters.category);
  }

  if (filters.subcategory) {
    query = query.eq('subcategory', filters.subcategory);
  }

  if (filters.search) {
    query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
  }

  if (filters.pricing) {
    query = query.eq('pricing->type', filters.pricing);
  }

  if (filters.verified !== undefined) {
    query = query.eq('is_verified', filters.verified);
  }

  // Admin-specific filters
  if (filters.aiGenerationStatus) {
    query = query.eq('ai_generation_status', filters.aiGenerationStatus);
  }

  if (filters.contentStatus) {
    query = query.eq('content_status', filters.contentStatus);
  }

  if (filters.submissionType) {
    query = query.eq('submission_type', filters.submissionType);
  }

  if (filters.hasEditorialReview !== undefined) {
    if (filters.hasEditorialReview) {
      query = query.not('editorial_review_id', 'is', null);
    } else {
      query = query.is('editorial_review_id', null);
    }
  }

  if (filters.qualityScoreMin !== undefined) {
    query = query.gte('content_quality_score', filters.qualityScoreMin);
  }

  if (filters.qualityScoreMax !== undefined) {
    query = query.lte('content_quality_score', filters.qualityScoreMax);
  }

  if (filters.lastScrapedAfter) {
    query = query.gte('last_scraped_at', filters.lastScrapedAfter);
  }

  if (filters.lastScrapedBefore) {
    query = query.lte('last_scraped_at', filters.lastScrapedBefore);
  }

  // Apply sorting
  const sortBy = filters.sortBy || 'created_at';
  const sortOrder = filters.sortOrder || 'desc';
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });

  // Apply pagination
  const page = filters.page || 1;
  const limit = filters.limit || 100; // Higher default limit for admin
  const from = (page - 1) * limit;
  const to = from + limit - 1;

  query = query.range(from, to);

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to fetch admin tools: ${error.message}`);
  }

  return {
    data: data?.map(transformDbToolToAITool) || [],
    pagination: {
      currentPage: page,
      totalPages: Math.ceil((count || 0) / limit),
      totalItems: count || 0,
      itemsPerPage: limit,
    },
  };
}

export async function getToolById(id: string): Promise<AITool | null> {
  const { data, error } = await supabase
    .from('tools')
    .select('*')
    .eq('id', id)
    .eq('content_status', 'published')
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return null; // Tool not found
    }
    throw new Error(`Failed to fetch tool: ${error.message}`);
  }

  return data ? transformDbToolToAITool(data) : null;
}

export async function getCategories(): Promise<AICategory[]> {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('title');

  if (error) {
    throw new Error(`Failed to fetch categories: ${error.message}`);
  }

  return data?.map(transformDbCategoryToAICategory) || [];
}

export async function getCategoriesWithTools(limit: number = 15): Promise<AICategory[]> {
  // Get all categories
  const categories = await getCategories();

  // For each category, get its tools, prioritizing complete tools
  const categoriesWithTools = await Promise.all(
    categories.map(async (category) => {
      // Get all tools for the category and sort them manually
      const { data: allCategoryTools, error } = await supabase
        .from('tools')
        .select('*')
        .eq('category_id', category.id)
        .eq('content_status', 'published')
        .order('created_at', { ascending: false });

      if (error) {
        console.error(`Failed to fetch tools for category ${category.id}:`, error);
        return {
          ...category,
          tools: [],
          totalToolsCount: 0,
        };
      }

      // Sort tools: complete tools (with features) first, then others
      const sortedTools = (allCategoryTools || []).sort((a, b) => {
        const aHasFeatures = a.features ? 1 : 0;
        const bHasFeatures = b.features ? 1 : 0;

        // First sort by features (complete tools first)
        if (aHasFeatures !== bHasFeatures) {
          return bHasFeatures - aHasFeatures;
        }

        // Then sort by creation date (newest first)
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });

      // Take only the first 'limit' tools
      const limitedTools = sortedTools.slice(0, limit);
      const transformedTools = limitedTools?.map(transformDbToolToAITool) || [];



      return {
        ...category,
        tools: transformedTools,
        totalToolsCount: transformedTools.length,
      };
    })
  );

  return categoriesWithTools;
}

export async function getCategoryWithTools(categoryId: string): Promise<{
  category: AICategory;
  tools: AITool[];
}> {
  // Get category
  const { data: category, error: categoryError } = await supabase
    .from('categories')
    .select('*')
    .eq('id', categoryId)
    .single();

  if (categoryError) {
    throw new Error(`Failed to fetch category: ${categoryError.message}`);
  }

  // Get tools for category
  const { data: tools, error: toolsError } = await supabase
    .from('tools')
    .select('*')
    .eq('category_id', categoryId)
    .eq('content_status', 'published')
    .order('created_at', { ascending: false });

  if (toolsError) {
    throw new Error(`Failed to fetch tools: ${toolsError.message}`);
  }

  return {
    category: transformDbCategoryToAICategory(category),
    tools: tools?.map(transformDbToolToAITool) || [],
  };
}

export async function createTool(toolData: Partial<AITool>): Promise<AITool> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  // Filter out fields that don't exist in the database
  const {
    founded, // Remove founded - it doesn't exist in current schema
    ...filteredData
  } = toolData;

  // Generate a unique ID from the slug or name
  const baseId = filteredData.slug || filteredData.name?.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '') || 'tool';

  // Add timestamp to ensure uniqueness
  const timestamp = Date.now();
  const uniqueId = `${baseId}-${timestamp}`;

  // Prepare the final data with the generated ID
  const finalData = {
    ...filteredData,
    id: uniqueId,
  };

  const { data, error } = await supabaseAdmin
    .from('tools')
    .insert([finalData])
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create tool: ${error.message}`);
  }

  return data;
}

export async function updateTool(id: string, updates: Partial<AITool>): Promise<AITool> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  const { data, error } = await supabaseAdmin
    .from('tools')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to update tool: ${error.message}`);
  }

  return data;
}

export async function deleteTool(id: string): Promise<void> {
  if (!supabaseAdmin) {
    throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
  }

  const { error } = await supabaseAdmin
    .from('tools')
    .delete()
    .eq('id', id);

  if (error) {
    throw new Error(`Failed to delete tool: ${error.message}`);
  }
}

// Search functionality
export async function searchTools(query: string, limit: number = 10): Promise<AITool[]> {
  const { data, error } = await supabase
    .from('tools')
    .select('*')
    .or(`name.ilike.%${query}%,description.ilike.%${query}%,features.cs.["${query}"]`)
    .eq('content_status', 'published')
    .limit(limit);

  if (error) {
    throw new Error(`Search failed: ${error.message}`);
  }

  return data?.map(transformDbToolToAITool) || [];
}

// Tool submission functions
export async function submitTool(submission: {
  name: string;
  url: string;
  description: string;
  category: string;
  subcategory?: string;
  submitterName?: string;
  submitterEmail: string;
  logoUrl?: string;
  tags?: string[];
  pricingType?: string;
}): Promise<void> {
  const { error } = await supabase
    .from('tool_submissions')
    .insert([{
      ...submission,
      status: 'pending',
      submitted_at: new Date().toISOString(),
    }]);

  if (error) {
    throw new Error(`Failed to submit tool: ${error.message}`);
  }
}

// Reviews functionality
export async function getToolReviews(toolId: string): Promise<any[]> {
  const { data, error } = await supabase
    .from('reviews')
    .select('*')
    .eq('tool_id', toolId)
    .eq('is_approved', true)
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to fetch reviews: ${error.message}`);
  }

  return data || [];
}

export async function submitReview(review: {
  toolId: string;
  userName: string;
  userEmail?: string;
  rating: number;
  title?: string;
  content: string;
}): Promise<void> {
  const { error } = await supabase
    .from('reviews')
    .insert([{
      tool_id: review.toolId,
      user_name: review.userName,
      user_email: review.userEmail,
      rating: review.rating,
      title: review.title,
      content: review.content,
      is_approved: false, // Requires moderation
      created_at: new Date().toISOString(),
    }]);

  if (error) {
    throw new Error(`Failed to submit review: ${error.message}`);
  }
}
